'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  AppShell,
  Navbar,
  Header,
  Text,
  NavLink,
  Button,
  Group,
  Loader,
  Center,
} from '@mantine/core';
import {
  IconDashboard,
  IconPackage,
  IconMessages,
  IconLogout,
  IconSettings,
  IconTestPipe,
  IconMessageCircle,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';

interface AuthUser {
  id: string;
  username: string;
  email: string | null;
}

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const response = await fetch('/api/auth/me');
      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
      } else {
        router.push('/login');
      }
    } catch (error) {
      console.error('Auth check error:', error);
      router.push('/login');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' });
      notifications.show({
        title: 'Success',
        message: 'Logged out successfully',
        color: 'green',
      });
      router.push('/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  if (loading) {
    return (
      <Center h="100vh">
        <Loader size="lg" />
      </Center>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <AppShell
      navbar={{
        width: 250,
        breakpoint: 'sm',
      }}
      header={{ height: 60 }}
      padding="md"
    >
      <AppShell.Header>
        <Group h="100%" px="md" justify="space-between">
          <Text size="lg" fw={600}>
            Admin Dashboard
          </Text>
          <Group>
            <Text size="sm" c="dimmed">
              Welcome, {user.username}
            </Text>
            <Button
              variant="subtle"
              leftSection={<IconLogout size="1rem" />}
              onClick={handleLogout}
            >
              Logout
            </Button>
          </Group>
        </Group>
      </AppShell.Header>

      <AppShell.Navbar p="md">
        <NavLink
          href="/admin"
          label="Dashboard"
          leftSection={<IconDashboard size="1rem" />}
        />
        <NavLink
          href="/admin/products"
          label="Products"
          leftSection={<IconPackage size="1rem" />}
        />
        <NavLink
          href="/admin/messages"
          label="Messages"
          leftSection={<IconMessages size="1rem" />}
        />
        <NavLink
          href="/admin/chat"
          label="Chat Test"
          leftSection={<IconMessageCircle size="1rem" />}
        />
        <NavLink
          href="/admin/test"
          label="System Test"
          leftSection={<IconTestPipe size="1rem" />}
        />
        <NavLink
          href="/admin/settings"
          label="Settings"
          leftSection={<IconSettings size="1rem" />}
        />
      </AppShell.Navbar>

      <AppShell.Main>{children}</AppShell.Main>
    </AppShell>
  );
}
