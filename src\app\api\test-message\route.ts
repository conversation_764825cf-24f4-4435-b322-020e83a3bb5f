import { NextRequest, NextResponse } from 'next/server';

// Test endpoint to simulate Facebook Messenger messages
export async function POST(request: NextRequest) {
  try {
    const { message, sender_id } = await request.json();
    
    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Create a mock Facebook webhook payload
    const mockPayload = {
      object: 'page',
      entry: [
        {
          id: 'test_page_id',
          time: Date.now(),
          messaging: [
            {
              sender: {
                id: sender_id || 'test_user_123'
              },
              recipient: {
                id: 'test_page_id'
              },
              timestamp: Date.now(),
              message: {
                mid: `test_message_${Date.now()}`,
                text: message
              }
            }
          ]
        }
      ]
    };

    // Send the mock payload to our webhook
    const webhookUrl = `${request.nextUrl.origin}/api/webhook/facebook`;
    const webhookResponse = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(mockPayload),
    });

    const webhookResult = await webhookResponse.json();

    return NextResponse.json({
      success: true,
      message: 'Test message sent to webhook',
      webhook_response: webhookResult,
      mock_payload: mockPayload,
    });
  } catch (error) {
    console.error('Test message error:', error);
    return NextResponse.json(
      { error: 'Failed to send test message', details: error },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    instructions: {
      method: 'POST',
      body: {
        message: 'Your test message here',
        sender_id: 'optional_sender_id'
      },
      example: 'curl -X POST http://localhost:3000/api/test-message -H "Content-Type: application/json" -d \'{"message": "Hello, this is a test!"}\''
    }
  });
}
