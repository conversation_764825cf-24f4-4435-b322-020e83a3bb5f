import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { MessageInsert, ResponseInsert } from '@/types/database';

const VERIFY_TOKEN = process.env.FACEBOOK_VERIFY_TOKEN || 'your-verify-token';

// GET /api/webhook/facebook - Webhook verification
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const mode = searchParams.get('hub.mode');
    const token = searchParams.get('hub.verify_token');
    const challenge = searchParams.get('hub.challenge');

    if (mode === 'subscribe' && token === VERIFY_TOKEN) {
      console.log('Webhook verified');
      return new NextResponse(challenge);
    } else {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
  } catch (error) {
    console.error('Webhook verification error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/webhook/facebook - Handle incoming messages
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Log incoming webhook data for debugging
    console.log('Facebook webhook received:', JSON.stringify(body, null, 2));

    // Process each entry in the webhook payload
    for (const entry of body.entry || []) {
      console.log('Processing entry:', entry.id);
      for (const messaging of entry.messaging || []) {
        console.log('Processing messaging:', messaging);
        if (messaging.message) {
          await handleIncomingMessage(messaging);
        }
      }
    }

    return NextResponse.json({ status: 'ok' });
  } catch (error) {
    console.error('Webhook processing error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function handleIncomingMessage(messaging: any) {
  try {
    const senderId = messaging.sender.id;
    const messageText = messaging.message.text;
    const timestamp = messaging.timestamp;

    console.log(`Processing message from ${senderId}: "${messageText}"`);

    if (!messageText) {
      console.log('Skipping non-text message');
      return; // Skip non-text messages for now
    }

    // Save incoming message to database
    const messageData: MessageInsert = {
      sender_id: senderId,
      message_text: messageText,
      message_type: 'text',
      platform: 'facebook',
      metadata: {
        timestamp,
        raw_messaging: messaging,
      },
    };

    console.log('Saving message to database:', messageData);

    const { data: savedMessage, error: messageError } = await supabase
      .from('messages')
      .insert(messageData)
      .select()
      .single();

    if (messageError) {
      console.error('Error saving message:', messageError);
      return;
    }

    console.log('Message saved successfully:', savedMessage.id);

    // Process the message and generate response
    await processMessageAndRespond(savedMessage);
  } catch (error) {
    console.error('Error handling incoming message:', error);
  }
}

async function processMessageAndRespond(message: any) {
  try {
    // For now, we'll implement a simple keyword-based response system
    // In a full implementation, this would integrate with your chosen LLM provider
    
    const messageText = message.message_text.toLowerCase();
    let responseText = '';
    let confidenceScore = 0.5;

    // Check memory for matching responses
    const { data: memoryEntries } = await supabase
      .from('memory')
      .select('*')
      .eq('is_active', true);

    let bestMatch = null;
    let bestScore = 0;

    for (const entry of memoryEntries || []) {
      const keywords = entry.keywords || [];
      let score = 0;
      
      for (const keyword of keywords) {
        if (messageText.includes(keyword.toLowerCase())) {
          score += 1;
        }
      }
      
      if (score > bestScore && score > 0) {
        bestScore = score;
        bestMatch = entry;
      }
    }

    if (bestMatch && bestScore >= 1) {
      responseText = bestMatch.answer;
      confidenceScore = Math.min(bestScore / bestMatch.keywords.length, 1.0);
    } else {
      // Fallback response
      responseText = 'Thank you for your message. Our team will get back to you soon!';
      confidenceScore = 0.3;
    }

    // Save response to database
    const responseData: ResponseInsert = {
      message_id: message.id,
      response_text: responseText,
      response_type: 'auto',
      confidence_score: confidenceScore,
      llm_provider: 'keyword_matching',
    };

    const { error: responseError } = await supabase
      .from('responses')
      .insert(responseData);

    if (responseError) {
      console.error('Error saving response:', responseError);
      return;
    }

    // In a real implementation, you would send the response back to Facebook
    // using the Facebook Graph API
    console.log(`Would send response to ${message.sender_id}: ${responseText}`);
    
  } catch (error) {
    console.error('Error processing message:', error);
  }
}
