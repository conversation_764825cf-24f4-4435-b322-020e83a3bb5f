'use client';

import { useState, useEffect } from 'react';
import { Container, Title, Text, <PERSON>ton, Stack, Group, Card, Grid, Badge, Loader, Alert } from '@mantine/core';
import { IconLogin, IconDashboard, IconPackage, IconMessages, IconCheck, IconX, IconAlertCircle } from '@tabler/icons-react';
import Link from 'next/link';

interface SetupStatus {
  database: { status: string; details: string };
  tables: { status: string; details: string };
  admin_user: { status: string; details: string };
  llm: { status: string; details: string };
  message_endpoint: { status: string; details: string };
}

export default function Home() {
  const [setupStatus, setSetupStatus] = useState<SetupStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    checkSetupStatus();
    checkAuthStatus();
  }, []);

  const checkSetupStatus = async () => {
    try {
      const response = await fetch('/api/setup');
      const data = await response.json();
      if (data.success) {
        setSetupStatus(data.results);
      }
    } catch (error) {
      console.error('Setup check failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const checkAuthStatus = async () => {
    try {
      const response = await fetch('/api/auth/me');
      setIsAuthenticated(response.ok);
    } catch (error) {
      setIsAuthenticated(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'OK':
      case 'CONFIGURED':
      case 'EXISTS':
        return <Badge color="green" leftSection={<IconCheck size="0.8rem" />}>OK</Badge>;
      case 'CREATED':
        return <Badge color="blue" leftSection={<IconCheck size="0.8rem" />}>Created</Badge>;
      case 'PARTIAL':
        return <Badge color="yellow" leftSection={<IconAlertCircle size="0.8rem" />}>Partial</Badge>;
      case 'WEBHOOK_ERROR':
        return <Badge color="orange" leftSection={<IconAlertCircle size="0.8rem" />}>Webhook Issue</Badge>;
      case 'NOT_CONFIGURED':
        return <Badge color="gray" leftSection={<IconAlertCircle size="0.8rem" />}>Not Set</Badge>;
      case 'ERROR':
        return <Badge color="red" leftSection={<IconX size="0.8rem" />}>Error</Badge>;
      default:
        return <Badge color="gray">Unknown</Badge>;
    }
  };

  return (
    <Container size="lg" py="xl">
      <Stack align="center" gap="xl">
        <div style={{ textAlign: 'center' }}>
          <Title order={1} size="3rem" mb="md">
            Product Management System
          </Title>
          <Text size="xl" c="dimmed" mb="xl">
            Full-stack Next.js application with Mantine UI, Supabase, and third-party messaging integration
          </Text>
        </div>

        <Grid>
          <Grid.Col span={{ base: 12, md: 6 }}>
            <Card withBorder h="100%">
              <Stack>
                <IconPackage size="2rem" color="blue" />
                <Title order={3}>Product Management</Title>
                <Text c="dimmed">
                  Manage your product catalog with full CRUD operations, search functionality, and detailed specifications.
                </Text>
              </Stack>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, md: 6 }}>
            <Card withBorder h="100%">
              <Stack>
                <IconMessages size="2rem" color="green" />
                <Title order={3}>Message Handling</Title>
                <Text c="dimmed">
                  Handle customer messages from third-party platforms (Make.com, n8n, etc.) with automated responses and manual override capabilities.
                </Text>
              </Stack>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, md: 6 }}>
            <Card withBorder h="100%">
              <Stack>
                <IconDashboard size="2rem" color="orange" />
                <Title order={3}>Admin Dashboard</Title>
                <Text c="dimmed">
                  Comprehensive admin interface for managing products, viewing messages, and configuring system settings.
                </Text>
              </Stack>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, md: 6 }}>
            <Card withBorder h="100%">
              <Stack>
                <IconLogin size="2rem" color="purple" />
                <Title order={3}>Secure Authentication</Title>
                <Text c="dimmed">
                  Session-based authentication with JWT tokens and protected admin routes for secure access.
                </Text>
              </Stack>
            </Card>
          </Grid.Col>
        </Grid>

        <Group justify="center">
          {isAuthenticated ? (
            <Button
              component={Link}
              href="/admin"
              size="lg"
              leftSection={<IconDashboard size="1.2rem" />}
            >
              Go to Admin Dashboard
            </Button>
          ) : (
            <Button
              component={Link}
              href="/login"
              size="lg"
              leftSection={<IconLogin size="1.2rem" />}
            >
              Admin Login
            </Button>
          )}
        </Group>

        {/* System Status */}
        <Card withBorder bg="gray.0" mt="xl" w="100%">
          <Stack>
            <Group justify="space-between">
              <Title order={4}>System Status</Title>
              <Button size="xs" variant="subtle" onClick={checkSetupStatus} loading={loading}>
                Refresh
              </Button>
            </Group>

            {loading ? (
              <Group justify="center">
                <Loader size="sm" />
                <Text size="sm">Checking system status...</Text>
              </Group>
            ) : setupStatus ? (
              <Grid>
                <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
                  <Group justify="space-between">
                    <Text size="sm" fw={500}>Database:</Text>
                    {getStatusBadge(setupStatus.database.status)}
                  </Group>
                  <Text size="xs" c="dimmed">{setupStatus.database.details}</Text>
                </Grid.Col>

                <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
                  <Group justify="space-between">
                    <Text size="sm" fw={500}>Tables:</Text>
                    {getStatusBadge(setupStatus.tables.status)}
                  </Group>
                  <Text size="xs" c="dimmed">{setupStatus.tables.details}</Text>
                </Grid.Col>

                <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
                  <Group justify="space-between">
                    <Text size="sm" fw={500}>Admin User:</Text>
                    {getStatusBadge(setupStatus.admin_user.status)}
                  </Group>
                  <Text size="xs" c="dimmed">{setupStatus.admin_user.details}</Text>
                </Grid.Col>

                <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
                  <Group justify="space-between">
                    <Text size="sm" fw={500}>LLM Provider:</Text>
                    {getStatusBadge(setupStatus.llm.status)}
                  </Group>
                  <Text size="xs" c="dimmed">{setupStatus.llm.details}</Text>
                </Grid.Col>

                <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
                  <Group justify="space-between">
                    <Text size="sm" fw={500}>Message Endpoint:</Text>
                    {getStatusBadge(setupStatus.message_endpoint.status)}
                  </Group>
                  <Text size="xs" c="dimmed">{setupStatus.message_endpoint.details}</Text>
                </Grid.Col>
              </Grid>
            ) : (
              <Alert color="red" icon={<IconX size="1rem" />}>
                Failed to check system status
              </Alert>
            )}
          </Stack>
        </Card>

        <Card withBorder bg="blue.0" mt="md" w="100%">
          <Stack>
            <Group justify="space-between">
              <Title order={4}>Setup Instructions</Title>
              <Button
                size="xs"
                variant="subtle"
                component={Link}
                href="/api/test-webhook"
                target="_blank"
              >
                Test Integration
              </Button>
            </Group>
            <Text size="sm">
              1. Set up your Supabase database using the provided schema file
            </Text>
            <Text size="sm">
              2. Configure your environment variables in .env.local
            </Text>
            <Text size="sm">
              3. Visit this page to verify all connections are working
            </Text>
            <Text size="sm">
              4. Configure third-party platforms (Make.com, n8n) to send messages to /api/message
            </Text>
            <Text size="sm">
              5. Login to admin dashboard and start managing your products!
            </Text>
          </Stack>
        </Card>
      </Stack>
    </Container>
  );
}
