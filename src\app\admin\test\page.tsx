'use client';

import { useState } from 'react';
import {
  Stack,
  Title,
  Card,
  Button,
  TextInput,
  Group,
  Text,
  Code,
  Alert,
  Divider,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { IconSend, IconRefresh, IconCheck, IconX } from '@tabler/icons-react';

export default function TestPage() {
  const [testResults, setTestResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const messageForm = useForm({
    initialValues: {
      message: 'Hello, this is a test message!',
      sender_id: 'test_user_123',
    },
  });

  const testMessage = async (values: typeof messageForm.values) => {
    try {
      setLoading(true);
      const response = await fetch('/api/test-message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();
      
      if (response.ok) {
        notifications.show({
          title: 'Success',
          message: 'Test message sent successfully',
          color: 'green',
        });
        setTestResults(data);
      } else {
        notifications.show({
          title: 'Error',
          message: data.error || 'Failed to send test message',
          color: 'red',
        });
      }
    } catch (error) {
      console.error('Test message error:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to send test message',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const checkStatus = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/status');
      const data = await response.json();
      setTestResults(data);
      
      notifications.show({
        title: 'Status Updated',
        message: 'System status refreshed',
        color: 'blue',
      });
    } catch (error) {
      console.error('Status check error:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to check status',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Stack>
      <Title order={1}>System Testing</Title>

      <Card withBorder>
        <Stack>
          <Title order={3}>Test Facebook Messenger</Title>
          <Text size="sm" c="dimmed">
            Send a test message to simulate Facebook Messenger webhook
          </Text>
          
          <form onSubmit={messageForm.onSubmit(testMessage)}>
            <Stack>
              <TextInput
                label="Test Message"
                placeholder="Enter test message"
                {...messageForm.getInputProps('message')}
              />
              <TextInput
                label="Sender ID"
                placeholder="test_user_123"
                {...messageForm.getInputProps('sender_id')}
              />
              <Group>
                <Button
                  type="submit"
                  leftSection={<IconSend size="1rem" />}
                  loading={loading}
                >
                  Send Test Message
                </Button>
              </Group>
            </Stack>
          </form>
        </Stack>
      </Card>

      <Card withBorder>
        <Stack>
          <Group justify="space-between">
            <Title order={3}>System Status</Title>
            <Button
              size="sm"
              variant="outline"
              leftSection={<IconRefresh size="1rem" />}
              onClick={checkStatus}
              loading={loading}
            >
              Check Status
            </Button>
          </Group>
          
          {testResults && (
            <Stack>
              <Divider />
              
              {testResults.database && (
                <Group>
                  <Text fw={500}>Database:</Text>
                  {testResults.database.connected ? (
                    <IconCheck color="green" size="1rem" />
                  ) : (
                    <IconX color="red" size="1rem" />
                  )}
                  <Text size="sm" c="dimmed">
                    {testResults.database.details}
                  </Text>
                </Group>
              )}

              {testResults.messages && (
                <Group>
                  <Text fw={500}>Messages:</Text>
                  <Text>{testResults.messages.count} total</Text>
                  {testResults.messages.count > 0 && (
                    <Text size="sm" c="dimmed">
                      (Recent: {testResults.messages.recent.length})
                    </Text>
                  )}
                </Group>
              )}

              {testResults.responses && (
                <Group>
                  <Text fw={500}>Responses:</Text>
                  <Text>{testResults.responses.count} total</Text>
                </Group>
              )}

              {testResults.memory && (
                <Group>
                  <Text fw={500}>Memory Entries:</Text>
                  <Text>{testResults.memory.count} active</Text>
                </Group>
              )}

              {testResults.admin_users && (
                <Group>
                  <Text fw={500}>Admin Users:</Text>
                  <Text>{testResults.admin_users.count} total</Text>
                  {testResults.admin_users.exists ? (
                    <IconCheck color="green" size="1rem" />
                  ) : (
                    <IconX color="red" size="1rem" />
                  )}
                  <Text size="sm" c="dimmed">
                    Admin user exists: {testResults.admin_users.exists ? 'Yes' : 'No'}
                  </Text>
                </Group>
              )}

              <Divider />
              
              <Text fw={500}>Environment:</Text>
              {testResults.environment && (
                <Stack gap="xs">
                  <Text size="sm">NODE_ENV: {testResults.environment.node_env}</Text>
                  <Text size="sm">JWT Secret: {testResults.environment.jwt_secret ? '✓' : '✗'}</Text>
                  <Text size="sm">Supabase URL: {testResults.environment.supabase_url ? '✓' : '✗'}</Text>
                  <Text size="sm">Facebook Verify Token: {testResults.environment.facebook_verify ? '✓' : '✗'}</Text>
                  <Text size="sm">Facebook Page Token: {testResults.environment.facebook_page ? '✓' : '✗'}</Text>
                </Stack>
              )}

              <Divider />
              
              <details>
                <summary style={{ cursor: 'pointer', fontWeight: 500 }}>
                  Raw Response Data
                </summary>
                <Code block mt="sm">
                  {JSON.stringify(testResults, null, 2)}
                </Code>
              </details>
            </Stack>
          )}
        </Stack>
      </Card>

      <Alert color="blue">
        <Text fw={500}>Testing Instructions:</Text>
        <Text size="sm">
          1. Use "Send Test Message" to simulate a Facebook message
        </Text>
        <Text size="sm">
          2. Check the Messages page to see if it was saved
        </Text>
        <Text size="sm">
          3. Use "Check Status" to verify all systems are working
        </Text>
        <Text size="sm">
          4. For real Facebook testing, set up ngrok and configure webhook
        </Text>
      </Alert>
    </Stack>
  );
}
